/* eslint-disable @typescript-eslint/no-explicit-any */
export const isFunctionEmpty = (fn: () => void) => {
  return typeof fn === "function";
};

export const buildQuery = (params: any) => {
  const queryParts = [];

  for (const key in params) {
    if (params.hasOwnProperty(key)) {
      const value = params[key];

      if (key.startsWith("autogenerate-mul-array-") && Array.isArray(value)) {
        // Handle autogenerate-mul-array- with array values
        const arrayKey = key.slice("autogenerate-mul-array-".length);
        value.forEach((item) => {
          queryParts.push(
            `${encodeURIComponent(arrayKey)}=${encodeURIComponent(item)}`
          );
        });
      } else {
        // Handle other cases
        queryParts.push(
          `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
        );
      }
    }
  }

  return queryParts.length > 0 ? `?${queryParts.join("&")}` : "";
};

export const TOKEN = "TOKEN";

export const routes = {
  LOGIN: "/login",
  SING_UP: "/signup",
  FORGOT_PASSWORD: "/forgot-password",
  RESET_PASSWORD: "/reset-password",
  HOME: "/",
};

export const generateAvatarCanvas = (
  name: string = "U",
  size: number = 100,
  fontSize: number = 0.65
): string => {
  // Check if we're in a browser environment
  if (typeof window === "undefined" || typeof document === "undefined") {
    const firstLetter = name?.charAt(0)?.toUpperCase() || "U";
    const svgAvatar = `
      <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
        <circle cx="${size / 2}" cy="${size / 2}" r="${
      size / 2
    }" fill="#00000000"/>
        <text x="${size / 2}" y="${size / 2}" text-anchor="middle" dy="0.35em"
              fill="white" font-family="Arial, sans-serif"
              font-size="${
                size * fontSize
              }" font-weight="bold">${firstLetter}</text>
      </svg>
    `;

    // Use Buffer in Node.js, window.btoa in browser
    return `data:image/svg+xml;base64,${
      typeof Buffer !== "undefined"
        ? Buffer.from(svgAvatar).toString("base64")
        : window.btoa(svgAvatar)
    }`;
  }

  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");

  if (!ctx) {
    return "";
  }

  canvas.width = size;
  canvas.height = size;

  // Generate a consistent color hash (optional, right now fixed color is used)
  let hash = 0;
  for (let i = 0; i < name?.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }

  // Draw background circle
  ctx.fillStyle = "#00000000";
  ctx.beginPath();
  ctx.arc(size / 2, size / 2, size / 2, 0, 2 * Math.PI);
  ctx.fill();

  // Draw text
  ctx.fillStyle = "#FFFFFF";
  ctx.font = `bold ${size * fontSize}px Arial, sans-serif`;
  ctx.textAlign = "center";
  ctx.textBaseline = "alphabetic";

  const firstLetter: string = name?.charAt(0)?.toUpperCase() || "U";

  // Measure text
  const textMetrics = ctx.measureText(firstLetter);
  const textHeight =
    (textMetrics.actualBoundingBoxAscent || 0) +
    (textMetrics.actualBoundingBoxDescent || 0);

  const centerX = size / 2;
  const centerY =
    size / 2 + ((textMetrics.actualBoundingBoxAscent || 0) - textHeight / 2);

  ctx.fillText(firstLetter, centerX, centerY);

  return canvas.toDataURL("image/png");
};
