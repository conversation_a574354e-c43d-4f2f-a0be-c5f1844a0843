/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useEffect, useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { <PERSON>u, X, User, <PERSON>pu, Zap, Trophy, Info, Home } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/Components/ui/dropdown-menu";
import { getCookie, removeCookie } from "typescript-cookie";
import { TOKEN } from "@/utils/function";
import { useAuth } from "@/utils/Context/GlobalContext";
import useApiRequest from "@/utils/hook/useApiRequest";
import { getMeAPI } from "@/app/action";
import { Skeleton } from "../ui/skeleton";

interface User {
  id: string;
  name: string;
  email: string;
  // add more fields if needed
}

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const pathname = usePathname();
  const { isLogin, setIsLogin, userData, setUserData } = useAuth();
  const api = useApiRequest();

  const navigation = [
    { name: "Home", href: "/", icon: Home },
    { name: "Practice", href: "/practice", icon: Cpu },
    { name: "Contest", href: "/contest", icon: Trophy },
    { name: "About Us", href: "/about-us", icon: Info },
  ];

  const isActive = (path: string) => pathname === path;

  const logOutHandler = () => {
    removeCookie(TOKEN);
    setIsLogin(false);
    setUserData(null);
  };

  useEffect(() => {
    api.sendRequest(getMeAPI, (res: any) => {
      if (res?.data?.user) {
        setIsLogin(true);
        setUserData(res?.data?.user);
      }
    });
  }, []);

  return (
    <header className="bg-slate-800/90 backdrop-blur-sm select-none border-b border-slate-700/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center shadow-lg">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">ElectronicBit</h1>
                <p className="text-xs text-slate-400">
                  Let&apos;s Learn Electronics Bit by Bit
                </p>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                    isActive(item.href)
                      ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg"
                      : "text-slate-300 hover:text-white hover:bg-slate-700/50"
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </nav>

          {/* Login Button */}
          <div className="flex items-center space-x-4">
            {api.isLoading ? (
              <div className="hidden md:flex justify-center items-center">
                <Skeleton className="h-10 w-24 rounded-xl " />
              </div>
            ) : isLogin && userData ? (
              <div className="hidden md:flex justify-center items-center">
                <DropdownMenu>
                  <DropdownMenuTrigger className="" asChild>
                    <button className="outline-none bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg font-medium h-10 w-10  rounded-full text-xl  select-none">
                      {userData?.name?.slice(0, 2)?.toLocaleUpperCase()}
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    align="end"
                    sideOffset={0}
                    className="!mt-1"
                  >
                    <DropdownMenuItem
                      onClick={() => {
                        logOutHandler();
                      }}
                      className="px-5  cursor-pointer text-base font-medium"
                    >
                      Logout
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ) : (
              <div className="hidden md:flex items-center space-x-4">
                <Link
                  href="/login"
                  className="btn-accent flex items-center space-x-2"
                >
                  <User className="w-4 h-4" />
                  <span>Login</span>
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-slate-300 hover:text-white p-2"
            >
              {isMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-slate-800/90 backdrop-blur-sm border-t border-slate-700/50">
              {navigation.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium transition-all duration-200 ${
                      isActive(item.href)
                        ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg"
                        : "text-slate-300 hover:text-white hover:bg-slate-700/50"
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{item.name}</span>
                  </Link>
                );
              })}
              <Link
                href="/login"
                className="flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium text-slate-300 hover:text-white hover:bg-slate-700/50 transition-all duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                <User className="w-5 h-5" />
                <span>Login</span>
              </Link>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
